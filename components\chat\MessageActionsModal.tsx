import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TouchableWithoutFeedback,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useTheme } from '@/src/context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface MessageActionsModalProps {
  visible: boolean;
  onClose: () => void;
  messageId: string;
  isOwnMessage: boolean;
  onReaction: (messageId: string, emoji: string) => void;
  onReply: (messageId: string) => void;
  onDelete: (messageId: string) => void;
}

const MessageActionsModal: React.FC<MessageActionsModalProps> = ({
  visible,
  onClose,
  messageId,
  isOwnMessage,
  onReaction,
  onReply,
  onDelete,
}) => {
  const { colors, isDark } = useTheme();

  // Common emoji reactions
  const commonEmojis = ['👍', '❤️', '😂', '😮', '😢', '🙏'];

  const handleEmojiPress = (emoji: string) => {
    onReaction(messageId, emoji);
    onClose();
  };

  const handleReplyPress = () => {
    onReply(messageId);
    onClose();
  };

  const handleDeletePress = () => {
    Alert.alert(
      'Delete Message',
      'Are you sure you want to delete this message?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            onDelete(messageId);
            onClose();
          },
        },
      ]
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.surface }]}>
              {/* Handle bar */}
              <View style={[styles.handleBar, { backgroundColor: colors.border }]} />
              
              {/* Modal content */}
              <View style={styles.content}>
                {/* Emoji reactions section */}
                <View style={styles.section}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]} className="font-rubik-medium">
                    React with emoji
                  </Text>
                  <View style={styles.emojiContainer}>
                    {commonEmojis.map((emoji, index) => (
                      <TouchableOpacity
                        key={index}
                        style={[styles.emojiButton, { backgroundColor: colors.background }]}
                        onPress={() => handleEmojiPress(emoji)}
                        activeOpacity={0.7}
                      >
                        <Text style={styles.emoji}>{emoji}</Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {/* Action buttons section */}
                <View style={styles.section}>
                  {/* Reply button */}
                  <TouchableOpacity
                    style={[styles.actionButton, { backgroundColor: colors.background }]}
                    onPress={handleReplyPress}
                    activeOpacity={0.7}
                  >
                    <Ionicons 
                      name="arrow-undo" 
                      size={20} 
                      color={colors.primary} 
                    />
                    <Text style={[styles.actionText, { color: colors.text }]} className="font-rubik-regular">
                      Reply
                    </Text>
                  </TouchableOpacity>

                  {/* Delete button - only show for own messages */}
                  {isOwnMessage && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: colors.background }]}
                      onPress={handleDeletePress}
                      activeOpacity={0.7}
                    >
                      <Ionicons 
                        name="trash-outline" 
                        size={20} 
                        color="#FF3B30" 
                      />
                      <Text style={[styles.actionText, { color: '#FF3B30' }]} className="font-rubik-regular">
                        Delete
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </SafeAreaView>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '50%',
  },
  handleBar: {
    width: 40,
    height: 4,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  content: {
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    marginBottom: 12,
  },
  emojiContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  emojiButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  emoji: {
    fontSize: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  actionText: {
    fontSize: 16,
    marginLeft: 12,
  },
});

export default MessageActionsModal;
