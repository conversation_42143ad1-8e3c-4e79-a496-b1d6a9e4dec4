import React, { useRef } from 'react';
import { View, Animated } from 'react-native';
import { TapGestureHandler, State } from 'react-native-gesture-handler';
import { useDispatch } from 'react-redux';
import { showActionsModal } from '@/src/store/slices/chatUISlice';

interface TappableMessageProps {
  children: React.ReactNode;
  enabled?: boolean;
  messageId: string;
}

const TappableMessage: React.FC<TappableMessageProps> = ({
  children,
  enabled = true,
  messageId,
}) => {
  const dispatch = useDispatch();
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Tap handler to show actions modal
  const onTapHandlerStateChange = (event: any) => {
    if (!enabled) return;

    const { state } = event.nativeEvent;

    switch (state) {
      case State.BEGAN:
        // Visual feedback
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 50,
          useNativeDriver: true,
        }).start();
        break;

      case State.END:
        // Show actions modal
        dispatch(showActionsModal(messageId));
        // Quick reset
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 50,
          useNativeDriver: true,
        }).start();
        break;

      case State.CANCELLED:
      case State.FAILED:
        // Quick reset
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 50,
          useNativeDriver: true,
        }).start();
        break;
    }
  };

  if (!enabled) {
    return <View>{children}</View>;
  }

  return (
    <TapGestureHandler
      onHandlerStateChange={onTapHandlerStateChange}
      numberOfTaps={1}
    >
      <Animated.View
        style={{
          transform: [{ scale: scaleAnim }],
        }}
      >
        {children}
      </Animated.View>
    </TapGestureHandler>
  );
};

export default TappableMessage;
