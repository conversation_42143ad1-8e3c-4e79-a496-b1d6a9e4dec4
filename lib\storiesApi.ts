import { supabase } from "./supabase";
import { Platform } from "react-native";

interface Story {
  id: string;
  user_id: string;
  image_url: string;
  video_url?: string;
  thumbnail_url?: string;
  story_type?: 'image' | 'video';
  caption?: string;
  created_at: string;
  expires_at: string;
  viewed_by: string[];
  user: {
    username: string;
    avatar: string;
  };
}

export const storiesAPI = {
  uploadVideo: async (file: { uri: string; name: string; type: string }) => {
    try {
      // Check authentication
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("User not authenticated");
      }
      console.log("Authenticated user:", user.id);

      const fileExt = file.name.split(".").pop()?.toLowerCase() || "mp4";
      const filePath = `${user.id}/${Date.now()}.${fileExt}`;

      console.log("Uploading video from URI:", file.uri);

      // Normalize URI for Android
      let normalizedUri = file.uri;
      if (Platform.OS === "android" && !normalizedUri.startsWith("file://")) {
        normalizedUri = `file://${normalizedUri}`;
      }

      // Create FormData for the upload
      const formData = new FormData();
      formData.append("file", {
        uri: normalizedUri,
        name: file.name,
        type: file.type || `video/${fileExt}`,
      } as any);

      // Upload to Supabase Storage
      const { error } = await supabase.storage
        .from("stories")
        .upload(filePath, formData, {
          contentType: file.type || `video/${fileExt}`,
          upsert: false,
        });

      if (error) {
        console.error("Storage upload error:", error.message, error);
        throw new Error(`Failed to upload video: ${error.message}`);
      }

      // Get public URL
      const { data } = supabase.storage.from("stories").getPublicUrl(filePath);

      if (!data?.publicUrl) {
        throw new Error("Failed to get public URL for uploaded video");
      }

      return { filePath, publicUrl: data.publicUrl };
    } catch (error) {
      console.error("Error uploading video:", error);
      throw error;
    }
  },

  uploadImage: async (file: { uri: string; name: string; type: string }) => {
    try {
      // Check authentication
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("User not authenticated");
      }
      console.log("Authenticated user:", user.id);

      const fileExt = file.name.split(".").pop()?.toLowerCase() || "jpg";
      const filePath = `${user.id}/${Date.now()}.${fileExt}`;

      console.log("Uploading file from URI:", file.uri);

      // Normalize URI for Android
      let normalizedUri = file.uri;
      if (Platform.OS === "android" && !normalizedUri.startsWith("file://")) {
        normalizedUri = `file://${normalizedUri}`;
      }

      // Create FormData for the upload
      const formData = new FormData();
      formData.append("file", {
        uri: normalizedUri,
        name: file.name,
        type: `image/${fileExt === "jpg" ? "jpeg" : fileExt}`,
      } as any);

      // Upload to Supabase Storage
      const { error } = await supabase.storage
        .from("stories")
        .upload(filePath, formData, {
          contentType: `image/${fileExt === "jpg" ? "jpeg" : fileExt}`,
          upsert: false,
        });

      if (error) {
        console.error("Storage upload error:", error.message, error);
        throw new Error(`Failed to upload image: ${error.message}`);
      }

      // Get public URL
      const { data } = supabase.storage.from("stories").getPublicUrl(filePath);

      if (!data?.publicUrl) {
        throw new Error("Failed to get public URL for uploaded image");
      }

      return { filePath, publicUrl: data.publicUrl };
    } catch (error) {
      console.error("Error uploading image:", error);
      throw error;
    }
  },

  createStory: async (
    mediaUrl: string,
    userId: string,
    caption?: string,
    sharedContent?: {
      originalPostId?: string;
      originalReelId?: string;
      sharedFromUserId?: string;
    },
    storyType: 'image' | 'video' = 'image',
    thumbnailUrl?: string
  ) => {
    try {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      const storyData: any = {
        user_id: userId,
        caption,
        expires_at: expiresAt.toISOString(),
        viewed_by: [],
        is_shared_content: !!sharedContent,
        story_type: storyType,
      };

      // Set media URL based on story type
      if (storyType === 'video') {
        storyData.video_url = mediaUrl;
        storyData.thumbnail_url = thumbnailUrl;
        // For video stories, image_url can be the thumbnail or null
        storyData.image_url = thumbnailUrl || null;
      } else {
        storyData.image_url = mediaUrl;
      }

      // Add shared content tracking if provided
      if (sharedContent) {
        if (sharedContent.originalPostId) {
          storyData.original_post_id = sharedContent.originalPostId;
        }
        if (sharedContent.originalReelId) {
          storyData.original_reel_id = sharedContent.originalReelId;
        }
        if (sharedContent.sharedFromUserId) {
          storyData.shared_from_user_id = sharedContent.sharedFromUserId;
        }
      }

      const { data, error } = await supabase
        .from("stories")
        .insert(storyData)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create story: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error creating story:", error);
      throw error;
    }
  },

  getActiveStories: async (): Promise<Story[]> => {
    try {
      const { data, error } = await supabase
        .from("stories")
        .select(
          `
          id,
          user_id,
          image_url,
          video_url,
          thumbnail_url,
          story_type,
          caption,
          created_at,
          expires_at,
          viewed_by,
          profiles!stories_user_id_fkey (username, avatar_url)
          `
        )
        .gt("expires_at", new Date().toISOString())
        .order("created_at", { ascending: false })
        .limit(50);
  
      if (error) {
        throw new Error(`Failed to fetch stories: ${error.message}`);
      }
  
      const stories: Story[] = data.map((story) => ({
        id: story.id,
        user_id: story.user_id,
        image_url: story.image_url,
        video_url: story.video_url,
        thumbnail_url: story.thumbnail_url,
        story_type: story.story_type || 'image',
        caption: story.caption,
        created_at: story.created_at,
        expires_at: story.expires_at,
        viewed_by: story.viewed_by || [],
        user: {
          username: story.profiles.username,
          avatar: story.profiles.avatar_url || "",
        },
      }));
  
      return stories;
    } catch (error) {
      console.error("Error fetching stories:", error);
      throw error;
    }
  },

  getUserStories: async (userId: string): Promise<Story[]> => {
    try {
      const { data, error } = await supabase
        .from("stories")
        .select("*")
        .eq("user_id", userId)
        .gt("expires_at", new Date().toISOString())
        .order("created_at", { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch user stories: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error("Error fetching user stories:", error);
      throw error;
    }
  },

  markStoryAsViewed: async (storyId: string) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("User not authenticated");
      }

      const { data: story, error: fetchError } = await supabase
        .from("stories")
        .select("viewed_by")
        .eq("id", storyId)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch story: ${fetchError.message}`);
      }

      if (!story.viewed_by.includes(user.id)) {
        const { error: updateError } = await supabase
          .from("stories")
          .update({
            viewed_by: [...story.viewed_by, user.id],
          })
          .eq("id", storyId);

        if (updateError) {
          throw new Error(`Failed to update viewed_by: ${updateError.message}`);
        }
      }
    } catch (error) {
      console.error("Error marking story as viewed:", error);
      throw error;
    }
  },

  getFileView: (filePath: string) => {
    return supabase.storage.from("stories").getPublicUrl(filePath).data
      .publicUrl;
  },

  deleteFile: async (filePath: string) => {
    try {
      const { error } = await supabase.storage
        .from("stories")
        .remove([filePath]);
      if (error) {
        throw new Error(`Failed to delete file: ${error.message}`);
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      throw error;
    }
  },

  deleteStory: async (storyId: string) => {
    try {
      // First verify the story belongs to the current user
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("User not authenticated");
      }

      // Get the story with user_id verification
      const { data: story, error: fetchError } = await supabase
        .from("stories")
        .select("image_url, user_id")
        .eq("id", storyId)
        .eq("user_id", user.id) // Ensure the story belongs to current user
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch story: ${fetchError.message}`);
      }

      if (!story) {
        throw new Error(
          "Story not found or you don't have permission to delete it"
        );
      }

      // Delete the associated image file if it exists
      if (story.image_url) {
        // Extract the file path from the URL (should be in format: user_id/filename.ext)
        const urlParts = story.image_url.split("/");
        const filePath = urlParts.slice(-2).join("/"); // Get last two parts: user_id/filename.ext

        console.log(`🗑️ Attempting to delete storage file: ${filePath}`);

        try {
          const { error: storageError } = await supabase.storage
            .from("stories")
            .remove([filePath]);

          if (storageError) {
            console.warn("Storage deletion warning:", storageError.message);
          } else {
            console.log("✅ Storage file deleted successfully");
          }
        } catch (fileError) {
          console.warn("File already deleted or not found:", fileError);
        }
      }

      // Delete the story record
      const { error: deleteError } = await supabase
        .from("stories")
        .delete()
        .eq("id", storyId)
        .eq("user_id", user.id); // Double check ownership

      if (deleteError) {
        throw new Error(`Failed to delete story: ${deleteError.message}`);
      }

      return true;
    } catch (error) {
      console.error("Error deleting story:", error);
      throw error;
    }
  },
};
